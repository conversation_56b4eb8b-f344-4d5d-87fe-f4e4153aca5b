<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.travelsky.ppp.improved.infrastructure.mapper.db1.IssueListMapper">
    <select id="queryByCondition"
            parameterType="com.travelsky.ppp.improved.domain.repository.condition.IssueListQueryCondition"
            resultType="com.travelsky.ppp.improved.domain.repository.po.IssueListPo"
    >
        SELECT
            id,
            software_version,
            description,
            source,
            severity,
            priority,
            cause_analysis,
            solution,
            owner,
            issue_time,
            plan_resolve_time,
            actual_resolve_time,
            status,
            tracking_record,
            self_built
        FROM issue_list
        <where>
            <if test="softwareVersion != null and softwareVersion != ''">
                AND software_version LIKE CONCAT('%', #{softwareVersion}, '%')
            </if>
            <if test="description != null and description != ''">
                AND description LIKE CONCAT('%', #{description}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <!-- 单个月份查询：问题提出时间或实际解决时间在该月份内 -->
            <if test="queryMonth != null">
                AND issue_time <![CDATA[<=]]> #{queryMonth}
                AND (plan_resolve_time  <![CDATA[>=]]> #{queryMonth} OR plan_resolve_time is NULL)
            </if>

        </where>
        ORDER BY issue_time DESC
    </select>
</mapper>