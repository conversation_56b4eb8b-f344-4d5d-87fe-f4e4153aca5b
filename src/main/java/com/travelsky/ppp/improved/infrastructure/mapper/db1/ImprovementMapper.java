package com.travelsky.ppp.improved.infrastructure.mapper.db1;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travelsky.ppp.improved.domain.repository.condition.ImprovementsQueryCondition;
import com.travelsky.ppp.improved.domain.repository.po.ImprovementPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ClassName:ImprovementMapper
 * Package:com.travelsky.ppp.improved.infrastructure.mapper.db1
 * Description:
 *
 * @date:2025/6/16 10:38
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Mapper
public interface ImprovementMapper extends BaseMapper<ImprovementPo> {

    List<ImprovementPo> queryByCondition(ImprovementsQueryCondition condition);
}
