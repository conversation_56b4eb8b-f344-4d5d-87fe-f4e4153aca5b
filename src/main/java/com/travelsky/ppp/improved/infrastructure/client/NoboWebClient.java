package com.travelsky.ppp.improved.infrastructure.client;

import com.travelsky.ppp.framework.common.RestConnect;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

/**
 * ClassName:NoboWebClient
 * Package:com.travelsky.ppp.improved.infrastructure.client
 * Description:
 *
 * @date:2025/6/17 10:43
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Service
public class NoboWebClient {

    private final static String NOBO_BASE_URL = "http://apihub-gw.x/nobo/api";
    private final static String NOBO_TOKEN = "cHBwIzIwMjQwODA4MTMzNjMxI25vYm8=";

    private final RestConnect restConnect;

    public NoboWebClient(RestConnect restConnect) {
        this.restConnect = restConnect;
    }

    public <T> T requestWithGet(String url, HttpHeaders httpHeaders, Class<T> classType) {
        httpHeaders.add("token", NOBO_TOKEN);
        return restConnect.get(NOBO_BASE_URL+"/"+url,httpHeaders,classType);
    }

    public <T> T requestWithPost(String url, HttpHeaders httpHeaders, Class<T> classType) {
        return restConnect.post(NOBO_BASE_URL+"/"+url,httpHeaders,classType);
    }

    public <T> T requestWithPost(String url, HttpHeaders httpHeaders,Object params, Class<T> classType) {
        return restConnect.post(NOBO_BASE_URL+"/"+url,httpHeaders,params,classType);
    }
}
