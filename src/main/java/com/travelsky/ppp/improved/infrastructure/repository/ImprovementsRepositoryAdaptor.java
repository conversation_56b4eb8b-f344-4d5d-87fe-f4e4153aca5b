package com.travelsky.ppp.improved.infrastructure.repository;

import com.travelsky.ppp.improved.domain.repository.ImprovementsRepository;
import com.travelsky.ppp.improved.domain.repository.condition.ImprovementsQueryCondition;
import com.travelsky.ppp.improved.domain.repository.po.ImprovementPo;
import com.travelsky.ppp.improved.infrastructure.mapper.db1.ImprovementMapper;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * ClassName:ImprovementsRepositoryAdaptor
 * Package:com.travelsky.ppp.improved.infrastructure.repository
 * Description:
 *
 * @date:2025/6/16 11:00
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Repository
public class ImprovementsRepositoryAdaptor implements ImprovementsRepository {

    private final ImprovementMapper improvementMapper;

    public ImprovementsRepositoryAdaptor(ImprovementMapper improvementMapper) {
        this.improvementMapper = improvementMapper;
    }

    @Override
    public void saveImprovement(ImprovementPo improvement) {
        improvementMapper.insert(improvement);
    }

    @Override
    public List<ImprovementPo> queryByCondition(ImprovementsQueryCondition condition) {
        return improvementMapper.queryByCondition(condition);
    }

    @Override
    public void updateImprovement(ImprovementPo improvement) {
        improvementMapper.updateById(improvement);
    }
}
