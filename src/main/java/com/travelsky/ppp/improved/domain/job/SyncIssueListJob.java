package com.travelsky.ppp.improved.domain.job;

import com.travelsky.ppp.improved.domain.entity.ImproveItem;
import com.travelsky.ppp.improved.domain.entity.ImprovementReport;
import com.travelsky.ppp.improved.domain.entity.IssueList;
import com.travelsky.ppp.improved.domain.entity.MetaValue;
import com.travelsky.ppp.improved.infrastructure.client.NoboWebClient;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName:SyncIssueListJob
 * Package:com.travelsky.ppp.improved.domain.job
 * Description:
 *
 * @date:2025/6/17 10:54
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Service
@RequiredArgsConstructor
public class SyncIssueListJob {

    private final NoboWebClient noboWebClient;

    public void syncIssueList() {
        final ImprovementReport improvementReport = getImprovementReport();
        final List<ImproveItem> items = improvementReport.getItems();
        //只取待确认
        final List<IssueList> issueLists = items.stream()
                .filter(item -> item.getStatus() == 1)
                .map(item -> {
                    // 创建问题清单
                    IssueList issueList = new IssueList();
                    issueList.create(
                            getSoftwareVersion(item.getEntity()),
                            generationDescription(item),
                            MetaValue.fromImproveItem(item));
                    return issueList;
                }).collect(Collectors.toList());

    }

    /**
     * 查询改进内容
     * <AUTHOR>
     * @date 2025/6/17 11:17
     * @return com.travelsky.ppp.improved.domain.entity.ImprovementReport
     */
    private ImprovementReport getImprovementReport() {
        final YearMonth now = YearMonth.now();
        final String currentMoth = now.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String url = String.format("improveReport?month=%s&force=-1",currentMoth);
        return noboWebClient.requestWithGet(url, new HttpHeaders(), ImprovementReport.class);
    }

    /**
     * 截取软件版本信息
     * <AUTHOR>
     * @date 2025/6/17 11:28
     * @param entity
     * @return java.lang.String
     */
    private String getSoftwareVersion(String entity) {
        return entity.substring(0,entity.lastIndexOf("/"))+"_"+entity.substring(entity.lastIndexOf(":"));
    }

    /**
     * 生成描述信息
     * <AUTHOR>
     * @date 2025/6/17 11:36
     * @param item
     * @return java.lang.String
     */
    private String generationDescription(ImproveItem item) {
        final int monthValue = LocalDate.now().getMonthValue();
        return String.format("%d-%s，%s指标未达成目标，目标值设置为%s，该软件指标为%s",
                monthValue,
                getSoftwareVersion(item.getEntity()),
                item.getMetricName(),
                item.getTarget(),
                item.getMetricValue());
    }

}
