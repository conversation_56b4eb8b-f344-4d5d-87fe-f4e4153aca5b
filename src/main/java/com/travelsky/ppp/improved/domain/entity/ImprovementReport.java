package com.travelsky.ppp.improved.domain.entity;

import lombok.Data;

import java.util.List;

@Data
public class ImprovementReport {

    /** 创建时间，例如 2006-01-02T15:04:05-0700 */
    private String createTime;

    /** 操作者姓名，本系统则写“本系统” */
    private String empName;

    /** 操作者工号，2 代表本系统 */
    private String empNo;

    /** 生成方式：1=人工录入 / 2=本系统加工 / 3=第三方提供 */
    private Integer from;

    private Integer id;

    /** 改进项列表 */
    private List<ImproveItem> items;

    /** 名称，如 2024-09 */
    private String name;

    /** 数据提供者：工号、系统名等 */
    private String provider;

    /** 发布状态：1=未发布, 2=已发布 */
    private Integer status;

    /** 数据所代表的时间，如 2024-09 */
    private String time;

    /** 更新时间，例如 2006-01-02T15:04:05-0700 */
    private String updateTime;
}