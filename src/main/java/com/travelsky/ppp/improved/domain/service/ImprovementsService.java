package com.travelsky.ppp.improved.domain.service;

import com.travelsky.ppp.improved.controller.assembler.ImprovementAssembler;
import com.travelsky.ppp.improved.controller.dto.ImprovementDto;
import com.travelsky.ppp.improved.controller.dto.ImprovementsQueryDto;
import com.travelsky.ppp.improved.domain.entity.Improvement;
import com.travelsky.ppp.improved.domain.entity.ImprovementFactory;
import com.travelsky.ppp.improved.domain.repository.ImprovementsRepository;
import com.travelsky.ppp.improved.domain.repository.condition.ImprovementsQueryCondition;
import com.travelsky.ppp.improved.domain.repository.po.ImprovementPo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName:ImprovementsService
 * Package:com.travelsky.ppp.improved.domain.service
 * Description:
 *
 * @date:2025/6/16 10:53
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Service
@RequiredArgsConstructor
public class ImprovementsService {

    private final ImprovementsRepository improvementsRepository;
    private final ImprovementFactory improvementFactory;


    public List<ImprovementDto> queryByCondition(ImprovementsQueryDto condition){
        // 查询条件
        final ImprovementsQueryCondition queryCondition = ImprovementsQueryCondition.builder()
                .improveObject(condition.getImproveObject())
                .metricName(condition.getMetricName())
                .compliant(condition.isCompliant())
                .queryMonth(condition.getQueryMonth())
                .status(condition.getStatus())
                .build();
        final List<ImprovementPo> improvementPos = improvementsRepository.queryByCondition(queryCondition);
        // 实体转换
        return improvementPos.stream().map(item -> {
            final ImprovementDto improvementDto = new ImprovementDto();
            BeanUtils.copyProperties(item, improvementDto);
            if (improvementDto.getCurrentValue() != null && improvementDto.getTargetValue() != null) {
                // 是否达标 当前值 >= 目标值
                improvementDto.setCompliant(improvementDto.getCurrentValue() >= improvementDto.getTargetValue());
            }
            return improvementDto;
        }).collect(Collectors.toList());
    }

    /**
     * 改进
     * <AUTHOR>
     * @date 2025/6/16 14:25
     * @param improvementDto
     */
    public void ensureImprovements(ImprovementDto improvementDto) {
        Improvement improvement = ImprovementAssembler.toDo(improvementDto);
        // 改进进行中
        improvement.ensureImprovement();
        final ImprovementPo improvementPo = improvementFactory.createImprovement(improvement);

        improvementsRepository.updateImprovement(improvementPo);
    }

    /**
     * 修改改进状态结束改进任务
     * <AUTHOR>
     * @date 2025/6/16 14:53
     * @param id
     */
    public void endingImprovements(Long id) {
        Improvement improvement = Improvement.builder().build();
        // 改进结束
        improvement.endingImprovement();
        final ImprovementPo improvementPo = improvementFactory.createImprovement(improvement);
        improvementsRepository.updateImprovement(improvementPo);
    }
}
