package com.travelsky.ppp.improved.domain.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 改进项对象
 */
@Data
public class ImproveItem {

    @JsonProperty("Assignee")
    private String assignee;           // 负责人
    @JsonProperty("AssigneeNo")
    private String assigneeNo;         // 负责人工号
    @JsonProperty("AssigneeOrg")
    private String assigneeOrg;        // 负责人组织
    @JsonProperty("AssigneeOrgRef")
    private String assigneeOrgRef;     // 负责人组织外部引用

    /** 成本（人日），默认 -1；用 BigDecimal 方便表示浮点精度 */
    @JsonProperty("Cost")
    private BigDecimal cost = BigDecimal.valueOf(-1);

    /** 创建时间，如 2023-08 */
    @JsonProperty("CreateTime")
    private String createTime;

    /** 原始值 */
    @JsonProperty("CreateValue")
    private Double createValue;

    /** 预计达成时间，未设置用 "-" */
    @JsonProperty("Deadline")
    private String deadline;

    @JsonProperty("Desc")
    private String desc;               // 暂时未使用

    /** 实际达成时间，未达成用 "-" */
    @JsonProperty("DoneTime")
    private String doneTime;

    @JsonProperty("Entity")
    private String entity;
    @JsonProperty("EntityRef")
    private String entityRef;          // 实体外部引用
    @JsonProperty("EntityType")
    private Integer entityType;        // 1=组织/2=软件/3=模块/4=应用/5=员工/6=项目/7=合约

    @JsonProperty("ID")
    private Integer id;

    @JsonProperty("MetricID")
    private Integer metricID;
    @JsonProperty("MetricName")
    private String  metricName;

    /** 当月实际值 */
    @JsonProperty("MetricValue")
    private BigDecimal metricValue;

    /** 改进过程记录，默认 "-" */
    @JsonProperty("Record")
    private String record = "-";

    @JsonProperty("ReportID")
    private Integer reportID;

    /** 规则 ID：-1=自主改进；-2=基于模型生成 */
    @JsonProperty("RuleID")
    private Integer ruleID;

    @JsonProperty("RuleName")
    private String ruleName;

    /**
     * 状态：
     * 1=待确认,2=不改进,3=进行中,4=提前达成,
     * 5=达成,6=延期达成,7=未达成,8=达成后不稳定,9=达成后且稳定
     */
    @JsonProperty("Status")
    private Integer status;

    /** 目标值 */
    @JsonProperty("Target")
    private BigDecimal target;

    /** 总成本（人日） */
    @JsonProperty("TotalCost")
    private BigDecimal totalCost;
}