package com.travelsky.ppp.improved.domain.entity;

import java.math.BigDecimal;

/**
 * ClassName:OriginalValue
 * Package:com.travelsky.ppp.improved.domain.entity
 * Description:
 * 原始数据
 * @date:2025/6/17 13:23
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
public class OriginalValue {


    private String assignee;           // 负责人
    private String assigneeNo;         // 负责人工号
    private String assigneeOrg;        // 负责人组织
    private BigDecimal createValue;    //原始值
    private String deadline;           //预计达成时间
    private Integer entityType;        //实体类型
    private BigDecimal metricValue;    //当月实际值
    private BigDecimal target;         //目标值
}
