package com.travelsky.ppp.improved.domain.entity;

import com.travelsky.ppp.improved.domain.repository.po.ImprovementPo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * ClassName:ImprovementFactory
 * Package:com.travelsky.ppp.improved.domain.entity
 * Description:
 *
 * @date:2025/6/16 14:44
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Service
public class ImprovementFactory {

    public ImprovementPo createImprovement(Improvement improvement) {
        final ImprovementPo improvementPo = new ImprovementPo();
        BeanUtils.copyProperties(improvement, improvementPo);
        return improvementPo;
    }
}
