package com.travelsky.ppp.improved.domain.entity;

import lombok.Data;

/**
 * ClassName:OriginalValue
 * Package:com.travelsky.ppp.improved.domain.entity
 * Description:
 * 原始值对象
 *
 * @date:2025/6/17 13:23
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Data
public class MetaValue {


    private String assignee;           // 负责人
    private String assigneeNo;         // 负责人工号
    private String assigneeOrg;        // 负责人组织
    private Double createValue;       //原始值
    private Double metricValue;       //当月实际值即当前值
    private Double target;            //目标值
    private String deadline;           //预计达成时间
    private Integer entityType;        //实体类型
    private String metricName;     //指标名称
    private Double currentValue;  // 当前值

    public static MetaValue fromImproveItem(ImproveItem improveItem) {
        MetaValue metaValue = new MetaValue();
        metaValue.setAssignee(improveItem.getAssignee());
        metaValue.setAssigneeNo(improveItem.getAssigneeNo());
        metaValue.setAssigneeOrg(improveItem.getAssigneeOrg());
        metaValue.setCreateValue(improveItem.getCreateValue());
        metaValue.setMetricValue(improveItem.getMetricValue());
        metaValue.setTarget(improveItem.getTarget());
        metaValue.setDeadline(improveItem.getDeadline());
        metaValue.setEntityType(improveItem.getEntityType());
        metaValue.setMetricName(improveItem.getMetricName());
        return metaValue;
    }

}
