package com.travelsky.ppp.improved.domain.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * ClassName:IssueList
 * Package:com.travelsky.ppp.improved.domain.entity
 * Description:
 *
 * @date:2025/6/13 10:55
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Data
public class IssueList {

    /**
     * 主键
     */
    private Long id;

    /**
     * 软件版本
     */
    private String softwareVersion;

    /**
     * 问题描述 (Markdown)
     */
    private String description;

    /**
     * 来源
     */
    private String source;

    /**
     * 严重程度
     */
    private String severity;

    /**
     * 优先级
     */
    private String priority;

    /**
     * 原因分析 (Markdown)
     */
    private String causeAnalysis;

    /**
     * 解决措施
     */
    private String solution;

    /**
     * 责任人
     */
    private String owner;

    /**
     * 问题提出时间
     */
    private LocalDateTime issueTime = LocalDateTime.now();

    /**
     * 计划解决时间
     */
    private LocalDateTime planResolveTime;

    /**
     * 实际解决时间
     */
    private LocalDateTime actualResolveTime;

    /**
     * 当前状态
     */
    private String status;

    /**
     * 跟踪记录（如改进链接）
     */
    private String trackingRecord;

    /**
     * 是否是自建问题，自建不允许删除
     */
    private boolean selfBuilt;

    /** 是否需要改进 */
    private boolean needForImprovement;

    // 改进项内容
    private MetaValue metaValue;

    // 是否确认为问题
    private boolean confirmIssue;

    public IssueList create(String softwareVersion, String description, ImproveItem improveItem) {
        this.status = Status.TODO.name(); // 初始状态
        this.selfBuilt = false;// 非自建
        this.source = IssueSource.INTERNAL_AUDIT.getValue(); // 内部审计
        this.softwareVersion = softwareVersion;
        this.description = description;
        this.improveItem = improveItem;
        return this;
    }

    public IssueList selfBuild() {
        this.status = Status.TODO.name(); // 初始状态
        this.selfBuilt = true;// 自建
        this.issueTime= LocalDateTime.now() ; // 自动生成时间
        return this;
    }

    /**
     * 问题确认
     * <AUTHOR>
     * @date 2025/6/13 13:23
     * @return com.travelsky.ppp.improved.domain.entity.IssueList
     */
    public IssueList ensureIssue() {
        this.status = Status.IN_PROGRESS.name();
        return this;
    }

    /**
     * 问题确认
     * <AUTHOR>
     * @date 2025/6/13 13:23
     * @return com.travelsky.ppp.improved.domain.entity.IssueList
     */
    public IssueList ensureIsNotIssue() {
        this.status = Status.DONE.name();
        return this;
    }
}
