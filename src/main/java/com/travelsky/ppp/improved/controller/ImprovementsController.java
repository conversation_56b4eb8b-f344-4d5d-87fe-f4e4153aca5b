package com.travelsky.ppp.improved.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.travelsky.ppp.framework.common.Result;
import com.travelsky.ppp.framework.common.Views;
import com.travelsky.ppp.improved.controller.dto.ImprovementDto;
import com.travelsky.ppp.improved.controller.dto.ImprovementsQueryDto;
import com.travelsky.ppp.improved.domain.service.ImprovementsService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ClassName:ImprovementsController
 * Package:com.travelsky.ppp.improved.controller
 * Description:
 *
 * @date:2025/6/16 14:19
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@RestController
@RequestMapping("/improve")
public class ImprovementsController {

    private final ImprovementsService improvementsService;

    public ImprovementsController(ImprovementsService improvementsService) {
        this.improvementsService = improvementsService;
    }

    @GetMapping("/list")
    public Result<List<ImprovementDto>> query(@RequestBody
                                              ImprovementsQueryDto queryDto) {
        return Result.success(improvementsService.queryByCondition(queryDto));
    }

    /**
     * 确认改进
     * <AUTHOR>
     * @date 2025/6/16 14:52
     * @param improvementDto
     * @return com.travelsky.ppp.framework.common.Result<java.lang.Void>
     */
    @PostMapping("/ensure")
    public Result<Void> ensure(@RequestBody
                               @JsonView(value = Views.RequestUpdate.class)
                               ImprovementDto improvementDto) {
        improvementsService.ensureImprovements(improvementDto);
        return Result.success();
    }

    /**
     * 结束改进
     * <AUTHOR>
     * @date 2025/6/16 14:52
     * @param id
     * @return com.travelsky.ppp.framework.common.Result<java.util.List<com.travelsky.ppp.improved.controller.dto.ImprovementDto>>
     */
    @PostMapping("/ending/{id}")
    public Result<Void> ending(@PathVariable Long id) {
        improvementsService.endingImprovements(id);
        return Result.success();
    }

}
