package com.travelsky.ppp.improved.controller.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.travelsky.ppp.framework.common.Views;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * ClassName:IssueListDto
 * Package:com.travelsky.ppp.improved.controller.dto
 * Description:
 *
 * @date:2025/6/13 11:20
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IssueListDto {

    /**
     * 主键
     */
    @JsonView(Views.RequestUpdate.class)// 仅更新时序列化
    private Long id;

    /**
     * 问题描述 (Markdown)
     */
    @JsonView(Views.RequestAdd.class)
    private String description;

    /**
     * 软件版本
     */
    @JsonView(Views.RequestAdd.class)
    private String softwareVersion;

    /**
     * 来源
     */
    @JsonView(Views.RequestAdd.class)
    private String source;

    /**
     * 严重程度
     */
    @Pattern(regexp = "^(SEVERE|MODERATE|NORMAL|MINOR)$",message = "问题严重程度输入异常")
    @JsonView(Views.RequestAdd.class)
    private String severity;

    /**
     * 优先级
     */
    @Pattern(regexp = "^(HIGH|MEDIUM|LOW)$",message = "问题优先级输入异常")
    @JsonView(Views.RequestAdd.class)
    private String priority;

    /**
     * 原因分析 (Markdown)
     */
    @JsonView(Views.RequestAdd.class)
    private String causeAnalysis;

    /**
     * 解决措施
     */
    @JsonView(Views.RequestAdd.class)
    private String solution;

    /**
     * 责任人
     */
    @JsonView(Views.RequestAdd.class)
    private String owner;

    /**
     * 计划解决时间
     */
    @JsonView(Views.RequestAdd.class)
    private LocalDateTime planResolveTime;

    /**
     * 当前状态
     */
    @JsonView(Views.RequestUpdate.class)// 仅更新时序列化
    @Pattern(regexp = "^(TODO|IN_PROGRESS|READY|DONE)$",message = "问题状态输入异常")
    private String status;

    /**
     * 跟踪记录（如改进链接）
     */
    @JsonView(Views.RequestAdd.class)
    private String trackingRecord;

    /** 是否需要改进 */
    @JsonView(Views.RequestUpdate.class)// 仅更新时序列化
    private boolean needForImprovement;

    /** 确认是否为问题 */
    @JsonView(Views.RequestUpdate.class)// 仅更新时序列化
    private boolean confirmIssue;


}
